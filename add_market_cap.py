import pandas as pd
import os

def add_market_cap_column():
    """
    为股票交易数据添加流通市值列
    """
    # 文件路径
    trading_file = 'stock-trading-data-pro/bj430017.csv'
    basic_data_file = 'data/daily_basic_data_optimized.csv'
    output_file = 'stock-trading-data-pro/bj430017_with_market_cap.csv'
    
    # 检查文件是否存在
    if not os.path.exists(trading_file):
        print(f"错误: 找不到文件 {trading_file}")
        return
    
    if not os.path.exists(basic_data_file):
        print(f"错误: 找不到文件 {basic_data_file}")
        return
    
    try:
        # 读取股票交易数据
        print("正在读取股票交易数据...")
        trading_df = pd.read_csv(trading_file)
        print(f"交易数据读取完成，共 {len(trading_df)} 行数据")
        
        # 读取基础数据文件（分块读取以处理大文件）
        print("正在读取基础数据文件...")
        basic_data_chunks = []
        chunk_size = 100000  # 每次读取10万行
        
        for chunk in pd.read_csv(basic_data_file, chunksize=chunk_size):
            # 只保留需要的列
            chunk = chunk[['ts_code', 'trade_date', 'circ_mv']].copy()
            # 删除缺失值
            chunk = chunk.dropna()
            basic_data_chunks.append(chunk)
        
        # 合并所有块
        basic_df = pd.concat(basic_data_chunks, ignore_index=True)
        print(f"基础数据读取完成，共 {len(basic_df)} 行数据")
        
        # 转换日期格式为YYYYMMDD
        print("正在转换日期格式...")
        trading_df['trade_date'] = pd.to_datetime(trading_df['交易日期']).dt.strftime('%Y%m%d')
        basic_df['trade_date'] = pd.to_datetime(basic_df['trade_date']).dt.strftime('%Y%m%d')
        
        # 创建映射字典：{(ts_code, trade_date): circ_mv}
        print("正在创建数据映射...")
        market_cap_map = {}
        for _, row in basic_df.iterrows():
            key = (row['ts_code'], row['trade_date'])
            market_cap_map[key] = row['circ_mv']
        
        # 为交易数据添加流通市值列
        print("正在添加流通市值列...")
        trading_df['流通市值'] = None
        
        # 遍历交易数据，查找对应的流通市值
        for idx, row in trading_df.iterrows():
            stock_code = row['股票代码']
            trade_date = row['trade_date']
            key = (stock_code, trade_date)
            
            if key in market_cap_map:
                trading_df.at[idx, '流通市值'] = market_cap_map[key]
            else:
                print(f"警告: 未找到 {stock_code} 在 {trade_date} 的流通市值数据")
        
        # 保存结果
        print(f"正在保存结果到 {output_file}...")
        trading_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 显示统计信息
        total_rows = len(trading_df)
        non_null_rows = trading_df['流通市值'].notna().sum()
        null_rows = total_rows - non_null_rows
        
        print(f"\n处理完成！")
        print(f"总行数: {total_rows}")
        print(f"成功匹配流通市值: {non_null_rows} 行")
        print(f"未匹配: {null_rows} 行")
        print(f"结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")

if __name__ == "__main__":
    add_market_cap_column()
