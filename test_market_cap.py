import pandas as pd
import os

def test_add_market_cap():
    """
    测试添加流通市值列的功能
    """
    # 文件路径
    trading_file = 'stock-trading-data-pro/bj430017.csv'
    basic_data_file = 'data/daily_basic_data_optimized.csv'
    
    # 检查文件是否存在
    if not os.path.exists(trading_file):
        print(f"错误: 找不到文件 {trading_file}")
        return
    
    if not os.path.exists(basic_data_file):
        print(f"错误: 找不到文件 {basic_data_file}")
        return
    
    try:
        # 读取股票交易数据
        print("正在读取股票交易数据...")
        trading_df = pd.read_csv(trading_file)
        print(f"交易数据读取完成，共 {len(trading_df)} 行数据")
        print("列名:", trading_df.columns.tolist())
        
        # 读取基础数据文件的前几行来检查结构
        print("\n正在读取基础数据文件的前5行...")
        basic_sample = pd.read_csv(basic_data_file, nrows=5)
        print("基础数据列名:", basic_sample.columns.tolist())
        print("基础数据前5行:")
        print(basic_sample)
        
        # 检查是否有circ_mv列
        if 'circ_mv' not in basic_sample.columns:
            print("\n警告: 基础数据中没有找到'circ_mv'列")
            print("可用列:", basic_sample.columns.tolist())
        
        # 检查是否有ts_code和trade_date列
        required_cols = ['ts_code', 'trade_date']
        missing_cols = [col for col in required_cols if col not in basic_sample.columns]
        if missing_cols:
            print(f"\n警告: 基础数据中缺少必需列: {missing_cols}")
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_add_market_cap()
